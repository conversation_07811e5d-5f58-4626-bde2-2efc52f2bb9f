#!/usr/bin/env python3
"""
社交媒体数据配置管理
支持本地社交媒体数据源选择和配置
"""

import argparse
from typing import List, Optional
from pathlib import Path


class SocialMediaConfig:
    """社交媒体数据配置管理器"""
    
    def __init__(self):
        self.config = {
            # 本地社交媒体数据目录配置
            "local_data_directories": {
                "reddit": "social_media_data",
                "twitter": "twitter_data",  # 预留
                "stocktwits": "stocktwits_data"  # 预留
            },

            # 股票特定的数据目录配置
            "stock_specific_directories": {
                "AAPL": {
                    "reddit": "AAPL_social_media",
                    "twitter": "AAPL_twitter",
                    "stocktwits": "AAPL_stocktwits"
                },
                "MSFT": {
                    "reddit": "MSFT_social_media",
                    "twitter": "MSFT_twitter",
                    "stocktwits": "MSFT_stocktwits"
                },
                "NVDA": {
                    "reddit": "NVDA_social_media",
                    "twitter": "NVDA_twitter",
                    "stocktwits": "NVDA_stocktwits"
                }
            },
            
            # 社交媒体源配置
            "available_sources": {
                "reddit": {
                    "name": "Reddit",
                    "description": "Reddit社交媒体数据，包含情感分析和参与度指标",
                    "file_pattern": "reddit_{date}.json",
                    "enabled": True
                },
                "twitter": {
                    "name": "Twitter/X",
                    "description": "Twitter/X社交媒体数据",
                    "file_pattern": "twitter_{date}.json",
                    "enabled": False  # 暂未实现
                },
                "stocktwits": {
                    "name": "StockTwits",
                    "description": "StockTwits投资社区数据",
                    "file_pattern": "stocktwits_{date}.json",
                    "enabled": False  # 暂未实现
                }
            },
            
            # 默认配置
            "default_source": "reddit",
            "use_local_social_media": False,  # 默认为False，通过命令行参数设置
            "social_media_time_offset": 0,  # 小时偏移
            "base_data_directory": "social_media_data"
        }
    
    def get_social_media_directory(self, ticker: str, source: str = "reddit") -> Optional[str]:
        """获取指定股票和源的社交媒体数据目录"""
        if ticker in self.config["stock_specific_directories"]:
            if source in self.config["stock_specific_directories"][ticker]:
                return self.config["stock_specific_directories"][ticker][source]
        
        # 回退到默认目录
        if source in self.config["local_data_directories"]:
            return self.config["local_data_directories"][source]
        
        return None
    
    def get_social_media_file_path(self, ticker: str, date: str, source: str = "reddit") -> Optional[Path]:
        """
        获取指定股票、日期和源的社交媒体数据文件路径

        支持两种文件命名模式:
        1. 标准模式: reddit_YYYY-MM-DD.json
        2. 股票特定模式: TICKER_YYYY-MM-DD.json (如 AAPL_2024-01-15.json)

        优先检查股票特定模式，如果不存在则回退到标准模式
        """
        directory = self.get_social_media_directory(ticker, source)
        if not directory:
            return None

        if source not in self.config["available_sources"]:
            return None

        base_dir = Path(self.config["base_data_directory"])

        # 首先尝试股票特定的文件命名模式: TICKER_YYYY-MM-DD.json
        ticker_specific_filename = f"{ticker}_{date}.json"
        ticker_specific_path = base_dir / directory / ticker_specific_filename

        if ticker_specific_path.exists():
            return ticker_specific_path

        # 回退到标准文件命名模式: reddit_YYYY-MM-DD.json
        file_pattern = self.config["available_sources"][source]["file_pattern"]
        standard_filename = file_pattern.format(date=date)
        standard_path = base_dir / directory / standard_filename

        return standard_path
    
    def is_source_enabled(self, source: str) -> bool:
        """检查指定源是否启用"""
        if source in self.config["available_sources"]:
            return self.config["available_sources"][source]["enabled"]
        return False
    
    def get_enabled_sources(self) -> List[str]:
        """获取所有启用的社交媒体源"""
        return [
            source for source, config in self.config["available_sources"].items()
            if config["enabled"]
        ]
    
    def set_use_local_social_media(self, use_local: bool):
        """设置是否使用本地社交媒体数据"""
        self.config["use_local_social_media"] = use_local
    
    def should_use_local_social_media(self) -> bool:
        """检查是否应该使用本地社交媒体数据"""
        return self.config["use_local_social_media"]
    
    def set_social_media_time_offset(self, offset_hours: int):
        """设置社交媒体数据时间偏移（小时）"""
        self.config["social_media_time_offset"] = offset_hours
    
    def get_social_media_time_offset(self) -> int:
        """获取社交媒体数据时间偏移（小时）"""
        return self.config["social_media_time_offset"]


# 全局配置实例
_social_media_config = SocialMediaConfig()


def get_social_media_config() -> SocialMediaConfig:
    """获取全局社交媒体配置实例"""
    return _social_media_config


def add_social_media_config_args(parser: argparse.ArgumentParser):
    """向argparse解析器添加社交媒体配置参数"""
    social_media_group = parser.add_argument_group('社交媒体数据配置')
    
    social_media_group.add_argument(
        '--use_local_social_media',
        action='store_true',
        help='使用本地社交媒体数据而不是API调用'
    )
    
    social_media_group.add_argument(
        '--social_media_sources',
        nargs='+',
        choices=['reddit', 'twitter', 'stocktwits'],
        default=['reddit'],
        help='要使用的社交媒体数据源 (默认: reddit)'
    )
    
    social_media_group.add_argument(
        '--social_media_time_offset',
        type=int,
        default=0,
        help='社交媒体数据时间偏移（小时），用于调整数据获取时间 (默认: 0)'
    )
    
    social_media_group.add_argument(
        '--social_media_data_dir',
        type=str,
        default='social_media_data',
        help='社交媒体数据基础目录 (默认: social_media_data)'
    )


def setup_social_media_config_from_args(args):
    """从命令行参数设置社交媒体配置"""
    config = get_social_media_config()
    
    if hasattr(args, 'use_local_social_media'):
        config.set_use_local_social_media(args.use_local_social_media)
    
    if hasattr(args, 'social_media_time_offset'):
        config.set_social_media_time_offset(args.social_media_time_offset)
    
    if hasattr(args, 'social_media_data_dir'):
        config.config["base_data_directory"] = args.social_media_data_dir
    
    if hasattr(args, 'social_media_sources'):
        # 启用指定的源，禁用其他源
        for source in config.config["available_sources"]:
            config.config["available_sources"][source]["enabled"] = source in args.social_media_sources


def print_social_media_config():
    """打印当前社交媒体配置"""
    config = get_social_media_config()
    
    print("\n社交媒体数据配置:")
    print(f"  使用本地数据: {config.should_use_local_social_media()}")
    print(f"  数据目录: {config.config['base_data_directory']}")
    print(f"  时间偏移: {config.get_social_media_time_offset()} 小时")
    
    enabled_sources = config.get_enabled_sources()
    print(f"  启用的数据源: {', '.join(enabled_sources)}")
    
    print("  可用数据源:")
    for source, source_config in config.config["available_sources"].items():
        status = "✓" if source_config["enabled"] else "✗"
        print(f"    {status} {source_config['name']}: {source_config['description']}")


if __name__ == "__main__":
    # 测试配置
    config = get_social_media_config()
    
    print("测试社交媒体配置:")
    print(f"AAPL Reddit数据目录: {config.get_social_media_directory('AAPL', 'reddit')}")
    print(f"AAPL Reddit数据文件: {config.get_social_media_file_path('AAPL', '2025-07-01', 'reddit')}")
    print(f"启用的数据源: {config.get_enabled_sources()}")
    
    print_social_media_config()
